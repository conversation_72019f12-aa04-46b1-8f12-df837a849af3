{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "type-check": "tsc -b", "lint": "eslint .", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "coverage": "vitest run --coverage"}, "dependencies": {"@codemirror/highlight": "^0.19.8", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/language": "^6.11.0", "@codemirror/language-data": "^6.5.1", "@codemirror/rangeset": "^0.19.9", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.8", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@lezer/highlight": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.4", "@reduxjs/toolkit": "^2.7.0", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.5", "@tanstack/react-table": "^8.21.3", "@uiw/react-codemirror": "^4.23.12", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cytoscape": "^3.32.0", "lucide-react": "^0.506.0", "markdown-it": "^14.1.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-grid-layout": "^1.5.1", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-resizable-panels": "^3.0.2", "react-router-dom": "^7.5.3", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5"}, "devDependencies": {"@eslint/js": "^9.22.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/markdown-it": "^14.1.2", "@types/node": "^22.15.3", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-grid-layout": "^1.3.5", "@types/redux-mock-store": "^1.5.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.1.2", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^26.1.0", "redux-mock-store": "^1.5.5", "tw-animate-css": "^1.2.8", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.4", "vitest": "^3.1.2"}}