# Generated by Django 5.2 on 2025-05-29 07:11

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0003_theme'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='theme',
            name='is_default',
        ),
        migrations.CreateModel(
            name='Entity',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type', models.CharField(choices=[('note', 'Note'), ('template', 'Template'), ('media', 'Media'), ('view', 'View'), ('widget', 'Widget'), ('kanban', 'Kanban'), ('calendar', 'Calendar'), ('canvas', 'Canvas')], default='note', max_length=50)),
                ('title', models.Char<PERSON>ield(max_length=100)),
                ('content', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='api.entity')),
            ],
        ),
        migrations.DeleteModel(
            name='Note',
        ),
    ]
