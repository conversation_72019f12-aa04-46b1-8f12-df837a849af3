/**
 * GridLayout - Main grid container component using react-grid-layout
 */

import React, { useState, useCallback } from 'react';
import RGL, { WidthProvider, Layout } from 'react-grid-layout';
import { ViewContent, WidgetConfig, WidgetType, createDefaultWidget } from '@/types/widgetTypes';
import WidgetContainer from '@/components/widgets/WidgetContainer';
import { Button } from '@/components/ui/button';

// Import react-grid-layout CSS
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ReactGridLayout = WidthProvider(RGL);

interface GridLayoutProps {
  viewContent: ViewContent;
  onLayoutChange?: (widgets: WidgetConfig[]) => void;
  onWidgetUpdate?: (widgetId: string, updates: Partial<WidgetConfig>) => void;
  onWidgetDelete?: (widgetId: string) => void;
  onWidgetAdd?: (widget: WidgetConfig) => void;
  isEditable?: boolean;
}

const GridLayout: React.FC<GridLayoutProps> = ({
  viewContent,
  onLayoutChange,
  onWidgetUpdate,
  onWidgetDelete,
  onWidgetAdd,
  isEditable = true,
}) => {
  const [isDragging, setIsDragging] = useState(false);

  // Convert widget configs to react-grid-layout format
  const layoutItems: Layout[] = viewContent.widgets.map(widget => ({
    i: widget.id,
    x: widget.position.x,
    y: widget.position.y,
    w: widget.position.w,
    h: widget.position.h,
    minW: widget.position.minW,
    minH: widget.position.minH,
    maxW: widget.position.maxW,
    maxH: widget.position.maxH,
  }));

  const handleLayoutChange = useCallback((layout: Layout[]) => {
    if (!onLayoutChange || isDragging) return;

    // Update widget positions based on layout changes
    const updatedWidgets = viewContent.widgets.map(widget => {
      const layoutItem = layout.find(item => item.i === widget.id);
      if (layoutItem) {
        return {
          ...widget,
          position: {
            ...widget.position,
            x: layoutItem.x,
            y: layoutItem.y,
            w: layoutItem.w,
            h: layoutItem.h,
          },
        };
      }
      return widget;
    });

    onLayoutChange(updatedWidgets);
  }, [viewContent.widgets, onLayoutChange, isDragging]);

  const handleDragStart = useCallback(() => {
    setIsDragging(true);
  }, []);

  const handleDragStop = useCallback((layout: Layout[]) => {
    setIsDragging(false);
    handleLayoutChange(layout);
  }, [handleLayoutChange]);

  const handleResizeStop = useCallback((layout: Layout[]) => {
    handleLayoutChange(layout);
  }, [handleLayoutChange]);

  const handleAddWidget = useCallback((widgetType: WidgetType) => {
    if (!onWidgetAdd) return;

    // Find a good position for the new widget
    const existingPositions = viewContent.widgets.map(w => w.position);
    let newY = 0;
    let newX = 0;

    // Simple placement algorithm - find the first available spot
    if (existingPositions.length > 0) {
      const maxY = Math.max(...existingPositions.map(p => p.y + p.h));
      newY = maxY;
    }

    const newWidget = createDefaultWidget(widgetType, {
      x: newX,
      y: newY,
      w: 6, // Default width
      h: 4, // Default height
    });

    onWidgetAdd(newWidget);
  }, [onWidgetAdd, viewContent.widgets]);

  if (viewContent.widgets.length === 0) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-center p-8">
          <h3 className="text-lg font-semibold mb-2">No widgets yet</h3>
          <p className="text-gray-500 mb-4">
            Add widgets to start building your dashboard
          </p>
          {isEditable && (
            <div className="space-y-2">
              <Button
                onClick={() => handleAddWidget(WidgetType.GRAPH)}
                className="mr-2"
              >
                Add Graph Widget
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full p-4">
      <ReactGridLayout
        className="layout"
        layout={layoutItems}
        cols={viewContent.layout.cols}
        rowHeight={viewContent.layout.rowHeight}
        margin={viewContent.layout.margin}
        containerPadding={viewContent.layout.containerPadding}
        onLayoutChange={handleLayoutChange}
        onDragStart={handleDragStart}
        onDragStop={handleDragStop}
        onResizeStop={handleResizeStop}
        isDraggable={isEditable}
        isResizable={isEditable}
        useCSSTransforms={true}
        preventCollision={false}
        compactType="vertical"
      >
        {viewContent.widgets.map(widget => (
          <div key={widget.id} className="widget-grid-item">
            <WidgetContainer
              widget={widget}
              onUpdate={onWidgetUpdate}
              onDelete={onWidgetDelete}
              isEditable={isEditable}
            />
          </div>
        ))}
      </ReactGridLayout>
    </div>
  );
};

export default GridLayout;
