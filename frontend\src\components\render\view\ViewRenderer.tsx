/**
 * ViewRenderer - Main component for rendering dashboard views with widgets
 */

import React, { useCallback, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { Entity } from '@/models/Entity';
import { ViewContent, WidgetConfig, createDefaultViewContent } from '@/types/widgetTypes';
import { EntityRendererProps } from '@/components/ContentRenderer';
import { saveEntity, updateEntity } from '@/store/slices/entitySlice';
import GridLayout from '@/components/render/view/GridLayout';
import { Save } from 'lucide-react';
import { Input } from '@/components/Input';
import { validateNoteTitle } from '@/utils/common';
import { useAppDispatch } from '@/hooks';

interface ViewRendererProps extends EntityRendererProps {
  entityID: string;
}

const ViewRenderer: React.FC<ViewRendererProps> = ({ entityID }) => {
  const dispatch = useAppDispatch();
  const entity: Entity = useSelector((state: RootState) => state.entities.allEntities[entityID]);
  const [titleError, setTitleError] = useState<string | undefined>();

  let viewContent: ViewContent;
  try {
    viewContent = entity.content ? JSON.parse(entity.content) : createDefaultViewContent();
  } catch (error) {
    console.error('Error parsing view content:', error);
    viewContent = createDefaultViewContent();
  }

  const handleLayoutChange = useCallback((updatedWidgets: WidgetConfig[]) => {
    const updatedViewContent: ViewContent = {
      ...viewContent,
      widgets: updatedWidgets,
    };

    dispatch(updateEntity({
      noteID: entityID,
      content: JSON.stringify(updatedViewContent),
    }));
  }, [dispatch, entityID, viewContent]);

  const handleWidgetUpdate = useCallback((widgetId: string, updates: Partial<WidgetConfig>) => {
    const updatedWidgets = viewContent.widgets.map(widget =>
      widget.id === widgetId ? { ...widget, ...updates } : widget
    );

    const updatedViewContent: ViewContent = {
      ...viewContent,
      widgets: updatedWidgets,
    };

    dispatch(updateEntity({
      noteID: entityID,
      content: JSON.stringify(updatedViewContent),
    }));
  }, [dispatch, entityID, viewContent]);

  const handleWidgetDelete = useCallback((widgetId: string) => {
    const updatedWidgets = viewContent.widgets.filter(widget => widget.id !== widgetId);

    const updatedViewContent: ViewContent = {
      ...viewContent,
      widgets: updatedWidgets,
    };

    dispatch(updateEntity({
      noteID: entityID,
      content: JSON.stringify(updatedViewContent),
    }));
  }, [dispatch, entityID, viewContent]);

  const handleWidgetAdd = useCallback((widget: WidgetConfig) => {
    const updatedWidgets = [...viewContent.widgets, widget];

    const updatedViewContent: ViewContent = {
      ...viewContent,
      widgets: updatedWidgets,
    };

    dispatch(updateEntity({
      noteID: entityID,
      content: JSON.stringify(updatedViewContent),
    }));
  }, [dispatch, entityID, viewContent]);

  return (
    <div className="h-full w-full">
      <span
                className="w-5 h-5 p-0"
                onClick={(e: React.MouseEvent<HTMLElement>) => {
                    e.preventDefault();
                    if (entity?.id) {
                        dispatch(saveEntity(entity));
                    }
                }}
            >
                <Save className="w-5 h-5" />
            </span>
            <Input
                className="text-4xl h-15 p-0 py-1"
                value={entity?.title}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    const newTitle = e.currentTarget.value;

                    // Validate title
                    const validation = validateNoteTitle(newTitle);
                    if (!validation.isValid) {
                        setTitleError(validation.error);
                    } else {
                        setTitleError(undefined);
                    }

                    // Update note in store immediately for UI responsiveness
                    dispatch(
                        updateEntity({
                            noteID: entity?.id,
                            title: newTitle,
                            content: entity?.content,
                            parent: entity?.parent,
                        }),
                    );
                }}
            />
      <GridLayout
        viewContent={viewContent}
        onLayoutChange={handleLayoutChange}
        onWidgetUpdate={handleWidgetUpdate}
        onWidgetDelete={handleWidgetDelete}
        onWidgetAdd={handleWidgetAdd}
        isEditable={true}
      />
    </div>
  );
};

export default ViewRenderer;
