/**
 * ViewRenderer - Main component for rendering dashboard views with widgets
 */

import React, { useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store';
import { Entity } from '@/models/Entity';
import { ViewContent, WidgetConfig, createDefaultViewContent } from '@/types/widgetTypes';
import { EntityRendererProps } from '@/components/ContentRenderer';
import { updateEntity } from '@/store/slices/entitySlice';
import GridLayout from '@/components/render/view/GridLayout';

interface ViewRendererProps extends EntityRendererProps {
  entityID: string;
}

const ViewRenderer: React.FC<ViewRendererProps> = ({ entityID }) => {
  const dispatch = useDispatch();
  const entity: Entity = useSelector((state: RootState) => state.entities.allEntities[entityID]);

  if (!entity) {
    return (
      <div className="p-4 text-red-500">
        Error: View not found
      </div>
    );
  }

  let viewContent: ViewContent;
  try {
    viewContent = entity.content ? JSON.parse(entity.content) : createDefaultViewContent();
  } catch (error) {
    console.error('Error parsing view content:', error);
    viewContent = createDefaultViewContent();
  }

  const handleLayoutChange = useCallback((updatedWidgets: WidgetConfig[]) => {
    const updatedViewContent: ViewContent = {
      ...viewContent,
      widgets: updatedWidgets,
    };

    dispatch(updateEntity({
      noteID: entityID,
      content: JSON.stringify(updatedViewContent),
    }));
  }, [dispatch, entityID, viewContent]);

  const handleWidgetUpdate = useCallback((widgetId: string, updates: Partial<WidgetConfig>) => {
    const updatedWidgets = viewContent.widgets.map(widget =>
      widget.id === widgetId ? { ...widget, ...updates } : widget
    );

    const updatedViewContent: ViewContent = {
      ...viewContent,
      widgets: updatedWidgets,
    };

    dispatch(updateEntity({
      noteID: entityID,
      content: JSON.stringify(updatedViewContent),
    }));
  }, [dispatch, entityID, viewContent]);

  const handleWidgetDelete = useCallback((widgetId: string) => {
    const updatedWidgets = viewContent.widgets.filter(widget => widget.id !== widgetId);

    const updatedViewContent: ViewContent = {
      ...viewContent,
      widgets: updatedWidgets,
    };

    dispatch(updateEntity({
      noteID: entityID,
      content: JSON.stringify(updatedViewContent),
    }));
  }, [dispatch, entityID, viewContent]);

  const handleWidgetAdd = useCallback((widget: WidgetConfig) => {
    const updatedWidgets = [...viewContent.widgets, widget];

    const updatedViewContent: ViewContent = {
      ...viewContent,
      widgets: updatedWidgets,
    };

    dispatch(updateEntity({
      noteID: entityID,
      content: JSON.stringify(updatedViewContent),
    }));
  }, [dispatch, entityID, viewContent]);

  return (
    <div className="h-full w-full">
      <GridLayout
        viewContent={viewContent}
        onLayoutChange={handleLayoutChange}
        onWidgetUpdate={handleWidgetUpdate}
        onWidgetDelete={handleWidgetDelete}
        onWidgetAdd={handleWidgetAdd}
        isEditable={true}
      />
    </div>
  );
};

export default ViewRenderer;
